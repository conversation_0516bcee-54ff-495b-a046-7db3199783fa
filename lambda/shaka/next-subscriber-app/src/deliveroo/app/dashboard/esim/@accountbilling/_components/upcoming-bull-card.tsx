import { Divider } from '@/components/divider/divider';
import { PlainCard } from '@/components/plain-card/plain-card';
import { FullDetailsButton } from '@/components/full-details-button/full-details-button';
import { GenericModal } from '@/components/generic-modal/generic-modal';

export function UpcomingBillCard() {
  return (
    <PlainCard
      as="article"
      className="mb-3 rounded border-none p-4 pb-0 shadow-none lg:pb-4"
    >
      <header>
        <h3 >Upcoming bills</h3>
      </header>
      <Divider className="mt-2" />
      <div className="grid grid-cols-2 grid-rows-[1fr_auto_auto] place-items-start gap-y-0 lg:grid-cols-[110px_2fr_auto] lg:grid-rows-[1fr_auto] lg:gap-x-4 xl:grid-cols-[110px_1fr_1fr_1fr] xl:grid-rows-1">
        <div>
          <p className="text-xxxs">February 2025</p>
          <strong className="block text-xs">£34</strong>
        </div>
        <div className="col-start-2 flex items-center gap-2 justify-self-end lg:justify-self-start">
          <div className="bg-warning-border h-2 w-2 rounded-full"></div>
          <p className="text-xxxs inline">Due 22/08/25</p>
        </div>
        <div className="col-span-2 col-start-2 row-start-2 justify-self-end lg:col-span-3 lg:col-start-1 xl:col-span-1 xl:col-start-3 xl:row-start-1">
          <p className="text-xxxs text-right font-bold xl:text-left">
            Paid automatically from card ending 2199
          </p>
        </div>
        <FullDetailsButton
          className="col-start-2 mt-2 ml-auto lg:col-start-3 lg:mt-0 xl:col-start-4"
          text="View details"
        >
          {({ isOpen, setIsOpen }) =>
            isOpen && (
              <GenericModal
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                modalTitle="hello"
                modalDescription="hi"
              />
            )
          }
        </FullDetailsButton>
      </div>

      <Divider className="mb-6 block lg:hidden" />
    </PlainCard>
  );
}
