'use client';

import React, { Suspense, useEffect } from 'react';
import { StripeProvider } from '@/lib/stripe/stripe-provider';
import { StripeMode } from '@/lib/stripe/types';
import Wrapper from '@/components/wrapper/wrapper';
import { ConditionalWrapper } from '@/components/conditional-wrapper/conditional-wrapper';
import { Divider } from '@/components/divider/divider';
import { useSearchParams } from 'next/navigation';
import Modal from '@/components/modal/modal';
import { CloseIcon, LoadingSpinner } from '@/icons/icons';
import { FullDetailsButton } from '@/components/full-details-button/full-details-button';
import { usePlanSelection } from '@/src/deliveroo/app/signup/context/SignupContext';
import { StripeCheckoutElementsOptions } from '@stripe/stripe-js';
import { useCreateCheckoutSession } from '@/hooks/useCreateCheckoutSession';
import { Alert } from '@/components/alert/alert';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { SignupProgressBar } from '@/src/deliveroo/app/signup/_components/signup-progress-bar/signup-progress-bar';
import { getClientConfig } from '@/client-config/client-config';
import { ExpressCheckoutButtons } from './_components/express-checkout-buttons';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { penniesToPoundsString } from '@/utils/formatters';
import { OrderSummary } from '@/src/deliveroo/app/signup/_components/order-summary/order-summary';
import { PlainCard } from '@/components/plain-card/plain-card';
import { Loader } from '@/components/loader/loader';
import { RequireAuth } from '@/components/require-auth/require-auth';
import { MAX_MAIN_PLAN_QUANTITY } from '@/src/deliveroo/app/signup/_reducer/reducer';
import { PaymentForm } from '@/src/deliveroo/app/signup/payment/_components/payment-form';
// import { RequireStepAuth } from '@/components/signup-progress-check/signup-progress-check';

const elementsOptions: StripeCheckoutElementsOptions = {
  appearance: {
    theme: 'flat',
    rules: {
      '.AccordionItem': {
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        paddingLeft: '1px',
        paddingRight: '1px'
      },
      '.Label': {
        color: '#141414',
        fontWeight: '700',
        marginBottom: '8px'
      },
      '.Input': {
        marginBottom: '8px',
        borderRadius: '2px',
        color: '#141414',
        backgroundColor: '#fff',
        border: '1px solid #dddde0'
      },
      '.Input:focus': {
        boxShadow: 'none',
        outline: '1px solid  #141414'
      },
      '.Input--invalid': {
        boxShadow: 'none',
        color: '#ea0040'
      },
      '.Error': {
        color: '#ea0040',
        fontSize: '14px'
      }
    },
    variables: {
      colorPrimary: '#141414',
      // colorBackground: 'transparent',
      iconCardErrorColor: '#141414',
      iconCardCvcErrorColor: '#141414'
    }
  }
};
const paymentMode: StripeMode = 'custom';
const returnUrl = ROUTES_CONFIG['payment-return'].path;

export default function PaymentPage() {
  return (
    <RequireAuth<typeof ROUTES_CONFIG>
      loadingComponent={<Loader />}
      redirectTo={ROUTES_CONFIG['plan-selection'].path}
    >
      <Suspense fallback={<LoadingPayment />}>
        <PaymentContent />
      </Suspense>
    </RequireAuth>
  );
}

function LoadingPayment() {
  return (
    <Wrapper>
      <div className="flex h-full w-full flex-col items-center justify-center p-4">
        <LoadingSpinner />
        <p className="mt-4 text-[18px]">Loading payment details...</p>
      </div>
    </Wrapper>
  );
}

const clientId = getClientConfig().clientId;

function PaymentContent() {
  const searchParams = useSearchParams();
  const errorParam = searchParams.get('error');
  const { orderSummary, backendPlanSelectionBasket } = usePlanSelection(
    MAX_MAIN_PLAN_QUANTITY
  );

  const { createCheckoutSession, clientSecret, isPending, error } =
    useCreateCheckoutSession();

  // Paw ! ideally in click handlers not use feect
  // const { saveCurrentStep } = useCurrentSignupStep({
  //   requiresAuth: true,
  //   brand: 'deliveroo'
  // });
  //
  // useEffect(() => {
  //   saveCurrentStep();
  // }, [saveCurrentStep]);

  useEffect(() => {
    createCheckoutSession({
      clientId,
      paymentMode,
      returnUrl,
      basket: backendPlanSelectionBasket
    });
  }, [createCheckoutSession, backendPlanSelectionBasket]);

  const displayError =
    errorParam === 'payment_failed'
      ? 'Please check your card details and try again'
      : '';

  if (error) {
    return (
      <PlainCard className="mx-auto h-fit max-w-[827px] p-6">
        <Alert variant="error" message={error?.message} />
      </PlainCard>
    );
  }

  return (
    // <RequireStepAuth>
    <Wrapper>
      <div className="left-column">
        <ConditionalWrapper className="rounded border-none p-6 shadow-none">
          <SignupProgressBar />
          <h1 className="mb-4 lg:mb-0">Payment details</h1>
          <Divider className="mt-6 hidden lg:block" />
          {isPending ? (
            <Loader />
          ) : (
            <>
              <FormAlert
                variant="deliveroo"
                singleMessageStyles=""
                messages={[
                  `message: Your first payment will be prorated, so we will take ${penniesToPoundsString(
                    clientSecret?.totalToPay
                  )} today and then £15 on the 1st of each month.`
                ]}
                dismissible={false}
              />
              <Divider className="mt-6" />
              <StripeProvider
                clientSecret={clientSecret?.clientSecret}
                mode={paymentMode}
                options={{
                  appearance: elementsOptions.appearance
                }}
              >
                <ExpressCheckoutButtons />
                <Divider className="mt-6" />
                <PaymentForm includeExpress={false} />
              </StripeProvider>
            </>
          )}
          {errorParam && (
            <FormAlert
              className="mt-4"
              title="Payment failed"
              variant="error"
              titleStyles=""
              singleMessageStyles=""
              messages={[`error: ${displayError}`]}
            />
          )}
          <div className="my-4 text-center">
            <p className="text-xxxs inline">Full </p>
            <FullDetailsButton
              className="text-xxxs deliverooLink"
              text="terms and conditions"
            >
              {({ isOpen, setIsOpen }) =>
                isOpen && (
                  <Modal onOpenChange={setIsOpen} open={isOpen}>
                    <Modal.Overlay />
                    <Modal.Content className="w-full rounded-lg p-6 lg:max-w-2xl">
                      <div className="mb-4 flex justify-end">
                        <Modal.Close>
                          <CloseIcon />
                        </Modal.Close>
                      </div>
                      <Modal.Title className="mb-6 text-xl font-semibold">
                        hello
                      </Modal.Title>
                      <Modal.Description>hi</Modal.Description>
                    </Modal.Content>
                  </Modal>
                )
              }
            </FullDetailsButton>
            <p className="text-xxxs inline"> apply.</p>
          </div>
        </ConditionalWrapper>
      </div>
      <OrderSummary
        basketSummary={orderSummary.basketSummary}
        totalCost={orderSummary.totalCost}
      />
    </Wrapper>
    // </RequireStepAuth>
  );
}
