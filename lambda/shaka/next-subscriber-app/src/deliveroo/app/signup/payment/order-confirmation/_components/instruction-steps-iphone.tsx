import Image from 'next/image';
import gamma from '@/src/deliveroo/public/images/gamma.png';
import { useCopyToClipboard } from '@/hooks/useCopyToClipboard';
import React, { useState } from 'react';
import { Divider } from '@/components/divider/divider';
import Button from '@/components/button/button';
import { useIphoneInstallationCode } from '@/hooks/useIphoneInstallationCode';
import { IphoneSafariInfoAlert } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/iphone-safari-info-alert';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { Alert } from '@/components/alert/alert';

interface InstructionStepProps {
  stepNumber: number;
  title: string;
  description?: React.ReactNode;
  children?: React.ReactNode;
}

function InstructionStep({
  stepNumber,
  title,
  description,
  children
}: InstructionStepProps) {
  return (
    <li>
      <p className="text-[18px] font-bold">
        {stepNumber}. {title}
      </p>
      {description && <p >{description}</p>}
      {children}
    </li>
  );
}

interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  showDivider?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

function SectionHeader({
  title,
  subtitle,
  showDivider = false,
  size = 'md'
}: SectionHeaderProps) {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  return (
    <>
      <h2 className={`${sizeClasses[size]}`}>{title}</h2>
      {subtitle && <p className="mb-5">{subtitle}</p>}
      {showDivider && <Divider className="mb-4" />}
    </>
  );
}

interface CodeDisplayProps {
  code: string;
  isLoading?: boolean;
  loadingComponent?: React.ReactNode;
}

function CodeDisplay({
  code,
  isLoading = false,
  loadingComponent
}: CodeDisplayProps) {
  if (isLoading && loadingComponent) {
    return <>{loadingComponent}</>;
  }

  return <p className="font-bold text-[#307BF5]">{code}</p>;
}

interface CopyButtonProps {
  textToCopy: string;
  onCopySuccess?: () => void;
  onCopyError?: (error: Error) => void;
  disabled?: boolean;
}

function CopyButton({
  textToCopy,
  onCopySuccess,
  onCopyError,
  disabled = false
}: CopyButtonProps) {
  const [, copy] = useCopyToClipboard();
  const [copied, setCopied] = useState(false);
  const [ripple, setRipple] = useState(false);

  const handleCopy = async () => {
    try {
      await copy(textToCopy);
      setCopied(true);
      setRipple(true);
      onCopySuccess?.();

      setTimeout(() => setRipple(false), 600);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      onCopyError?.(error as Error);
    }
  };

  return (
    <Button
      disabled={disabled}
      onClick={handleCopy}
      className={`relative ml-auto min-w-[100px] overflow-hidden rounded-lg transition-all duration-300 ${
        copied ? 'bg-primary! border-primary!' : 'bg-black!'
      }`}
    >
      {ripple && <CopyButtonRipple />}
      <CopyButtonContent copied={copied} />
    </Button>
  );
}

function CopyButtonRipple() {
  return (
    <span className="absolute inset-0 flex items-center justify-center">
      <span className="bg-teal-light absolute h-full w-full animate-ping rounded-lg opacity-75" />
    </span>
  );
}

function CopyButtonContent({ copied }: { copied: boolean }) {
  return (
    <>
      <span
        className={`relative inline-flex transform items-center transition-all duration-300 ${
          copied ? 'scale-0 opacity-0' : 'scale-100'
        }`}
      >
        Copy
      </span>
      <span
        className={`absolute inset-0 flex transform items-center justify-center transition-all duration-300 ${
          copied ? 'scale-100' : 'scale-0 opacity-0'
        }`}
      >
        <svg
          className="mr-1.5 h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={3}
            d="M5 13l4 4L19 7"
          />
        </svg>
        Copied!
      </span>
    </>
  );
}

function LoadingCodeSkeleton() {
  return (
    <div className="mt-2 space-y-2">
      <div className="h-4 w-42 animate-pulse rounded bg-white" />
      <div className="h-4 w-32 animate-pulse rounded bg-white" />
    </div>
  );
}

function LoadingSkeleton() {
  return (
    <div className="mt-2 space-y-2">
      <div className="h-[107px] w-[380px] animate-pulse rounded bg-gray-400" />
      <div className="my-4 h-4 w-42 animate-pulse rounded bg-gray-400" />
      <div className="h-12 w-[380px] animate-pulse rounded bg-gray-400" />
    </div>
  );
}

interface InstallationOptionProps {
  optionNumber: number;
  title: string;
  children: React.ReactNode;
  showDivider?: boolean;
}

function InstallationOption({
  optionNumber,
  title,
  children,
  showDivider = true
}: InstallationOptionProps) {
  return (
    <>
      {showDivider && <Divider className="my-4" />}
      <h3 >
        Option {optionNumber} - {title}
      </h3>
      {children}
    </>
  );
}

function APNInstructions() {
  return (
    <>
      <ol className="space-y-6">
        <InstructionStep
          stepNumber={1}
          title="Navigate to Settings"
          description="Go to Mobile Service > Pick this SIM (likely labeled as 'Secondary' or 'Travel') > Mobile Data Network"
        />
        <InstructionStep stepNumber={2} title={'Change APN to "gamma"'}>
          <p className="mb-6">
            Delete &quot;three.co.uk&quot; in the APN field in the &quot;Mobile
            data&quot; section and type in{' '}
            <span className="font-bold">&quot;gamma&quot;</span>
          </p>
        </InstructionStep>
      </ol>
      <Image src={gamma} alt="Gamma setup instructions" />
    </>
  );
}

function EmailInstallationSection() {
  return (
    <p className="mb-5">
      We have just sent you an email. Open it on your iPhone and tap the{' '}
      <span className="font-bold">Install eSIM button</span>. Make sure you
      check your spam folder.
    </p>
  );
}

interface InstallationCodeBoxProps {
  installationCode: string;
  isLoading: boolean;
  onCopyError: () => void;
  hasError: boolean;
}

function InstallationCodeBox({
  installationCode,
  isLoading,
  onCopyError,
  hasError
}: InstallationCodeBoxProps) {
  const baseUrl = process.env.NEXT_PUBLIC_UNIVERSAL_LINK_URL;
  // const baseUrl = 'https://rideresim.com';
  const url = `${baseUrl}/activation-link`;
  const code = `${baseUrl}/signup/iphone-installation/?token=${installationCode}`;

  return (
    <div className="mt-4 grid grid-cols-2 gap-4 rounded-lg bg-[#ECECEC] p-4">
      <CodeDisplay
        code={url}
        isLoading={isLoading}
        loadingComponent={<LoadingCodeSkeleton />}
      />
      <CopyButton
        textToCopy={code}
        disabled={isLoading || hasError}
        onCopyError={onCopyError}
      />
    </div>
  );
}

function NonSafariInstallationSection() {
  // change name to reflect that it is a token - once u see the response
  const {
    installationCode,
    isPendingInstallationCode,
    iphoneInstallationCodeError
  } = useIphoneInstallationCode();
  const [copyError, setCopyError] = useState<string | null>(null);

  const handleCopyError = () => {
    setCopyError(
      'We couldn’t copy the code. Please try again or copy it manually.'
    );
  };

  return (
    <>
      <IphoneSafariInfoAlert className="bg-teal-light border-[#43CCBC]">
        To enhance security, Apple only allows 1-tap eSIM installation from the{' '}
        <span className="font-bold">Safari browser</span>
      </IphoneSafariInfoAlert>

      <ol className="mt-5 space-y-6">
        <InstructionStep stepNumber={1} title="Copy the following link">
          <InstallationCodeBox
            installationCode={installationCode}
            isLoading={isPendingInstallationCode}
            onCopyError={handleCopyError}
            hasError={!!iphoneInstallationCodeError}
          />
          {copyError && (
            <Alert variant="error" message={copyError} className="mt-4 w-fit" />
          )}
        </InstructionStep>

        <InstructionStep
          stepNumber={2}
          title="Open the link in Safari"
          description={
            <>
              Open the copied link in the Safari browser and click the{' '}
              <span className="font-bold">Install eSIM</span> button.
            </>
          }
        />
      </ol>
    </>
  );
}

export function InstructionStepsIos() {
  return (
    <>
      <SectionHeader
        title="Data settings"
        subtitle="You need to modify your settings to be able to use data."
      />
      <APNInstructions />
    </>
  );
}

interface InstructionStepsIosNonSafariProps {
  isEsimReady: boolean;
  shouldShowPendingState: boolean;
  hasPollingError: boolean;
}

export function InstructionStepsIosNonSafari({
  isEsimReady,
  shouldShowPendingState,
  hasPollingError
}: InstructionStepsIosNonSafariProps) {
  let content: React.ReactNode = null;

  if (shouldShowPendingState) {
    content = <LoadingSkeleton />;
  } else if (hasPollingError) {
    content = (
      <FormAlert
        variant="info"
        className="mt-4 w-fit p-6 text-left"
        messages={[
          'message: Activation link takes longer than expected. You can now go to the dashboard by clicking on the view my account button below. It will appear in instructions section.'
        ]}
      />
    );
  } else if (isEsimReady) {
    content = <NonSafariInstallationSection />;
  }
  return (
    <>
      <SectionHeader title="eSIM installation" size="sm" showDivider />
      <InstallationOption
        optionNumber={1}
        title="email installation"
        showDivider={false}
      >
        <EmailInstallationSection />
      </InstallationOption>
      <InstallationOption optionNumber={2} title="Safari installation">
        {content}
      </InstallationOption>
    </>
  );
}
