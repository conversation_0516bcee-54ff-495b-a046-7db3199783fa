import React, { useState, useEffect } from 'react';
import { CloseIcon, ErrorIcon, InfoIcon, UserIcon } from '@/icons/icons';

export type ErrorMessages = `${string}: ${string}`[];

const alertConfig = {
  error: {
    icon: <ErrorIcon stroke="black" />,
    bgColor: 'bg-error-subtle',
    borderColor: 'border-error',
    textColor: 'text-primary',
    iconColor: 'text-primary',
    errorMsgColor: 'text-primary',
    role: 'alert',
    ariaLabel: 'Error messages'
  },
  warning: {
    icon: <ErrorIcon stroke="black" />,
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    textColor: 'text-yellow-800',
    iconColor: 'text-yellow-500',
    errorMsgColor: 'text-yellow-800',
    role: 'alert',
    ariaLabel: 'Warning messages'
  },
  success: {
    icon: <InfoIcon />,
    bgColor: 'bg-mint-subtle',
    borderColor: 'border-success-border',
    textColor: 'text-primary',
    iconColor: 'text-primary',
    errorMsgColor: 'text-primary',
    role: 'status',
    ariaLabel: 'Success messages'
  },
  info: {
    icon: <ErrorIcon stroke="black" />,
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-800',
    iconColor: 'text-blue-500',
    errorMsgColor: 'text-blue-800',
    role: 'status',
    ariaLabel: 'Information messages'
  },
  deliveroo: {
    icon: <ErrorIcon stroke="black" />,
    bgColor: 'bg-teal-light',
    borderColor: 'border-[#43CCBC]',
    textColor: 'text-text',
    iconColor: 'text-red-500',
    errorMsgColor: 'text-text',
    role: 'status',
    ariaLabel: 'Success message'
  },
  deliverooInfo: {
    icon: <UserIcon />,
    bgColor: 'bg-[#FEF2E0]',
    borderColor: 'border-[#C70]',
    textColor: 'text-text',
    iconColor: 'text-red-500',
    errorMsgColor: 'text-text',
    role: 'status',
    ariaLabel: 'Success message'
  }
};

interface FormAlertProps {
  messages: ErrorMessages;
  variant?:
    | 'error'
    | 'warning'
    | 'info'
    | 'success'
    | 'deliveroo'
    | 'deliverooInfo';
  title?: string;
  dismissible?: boolean;
  onDismiss?: () => void;
  autoHide?: boolean;
  autoHideDelay?: number;
  className?: string;
  messagesStyles?: string;
  singleMessageStyles?: string;
  titleStyles?: string;
}

export function FormAlert({
  messages,
  variant = 'error',
  title = '',
  dismissible = true,
  onDismiss = () => {},
  autoHide = false,
  autoHideDelay = 5000,
  className = '',
  messagesStyles = '',
  singleMessageStyles = '',
  titleStyles = ''
}: FormAlertProps) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (autoHide && messages.length > 0) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, autoHideDelay);
      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideDelay, messages]);

  const handleDismiss = () => {
    setIsVisible(false);
    if (onDismiss) onDismiss();
  };

  if (!isVisible || messages.length === 0) return null;

  const config = alertConfig[variant];
  const singleErrorMessage = messages[0].split(':')[1];

  return (
    <div
      role={config.role}
      aria-live={
        variant === 'error' || variant === 'warning' ? 'assertive' : 'polite'
      }
      aria-label={config.ariaLabel}
      className={`${config.bgColor} ${config.borderColor} ${config.textColor} rounded-lg border p-4 ${className}`}
    >
      <div className="flex items-start gap-3">
        {config.icon}
        <div className="min-w-0 flex-1">
          {title && <h3 className={`text-xxs mb-2 ${titleStyles}`}>{title}</h3>}
          {messages.length === 1 ? (
            <ul className="text-default">
              <li
                className={`leading-5 ${config.errorMsgColor} ${singleMessageStyles}`}
                role="alert"
              >
                {singleErrorMessage}
              </li>
            </ul>
          ) : (
            <ul
              className={`text-default space-y-1 text-pretty ${messagesStyles}`}
            >
              {messages.map((error: string, index: number) => {
                const message = error.split(':')[1];
                return (
                  <li key={index} className="leading-5" role="alert">
                    {message}
                  </li>
                );
              })}
            </ul>
          )}
        </div>
        {dismissible && (
          <button
            onClick={handleDismiss}
            className={`${config.iconColor} -m-1 cursor-pointer rounded p-1 transition-opacity hover:opacity-75 focus:ring-2 focus:ring-current focus:ring-offset-1 focus:outline-none`}
            aria-label="Dismiss alert"
          >
            <CloseIcon className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
}
